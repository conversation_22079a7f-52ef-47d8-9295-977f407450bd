# Chatbot with Gemini API Integration

This chatbot has been enhanced to use the Gemini API for all responses, with project context included in each request.

## Setup Instructions

### Configure the Gemini API

To use the Gemini API:

1. Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Open `src/config.js` and add your API key:

```javascript
API: {
    GEMINI_API_KEY: "YOUR_API_KEY_HERE", // Add your Gemini API key here
    GEMINI_API_URL: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
    MAX_TOKENS: 200,
    TEMPERATURE: 0.7
}
```

### Customize Project Information

You can customize the project information that's sent with each API request by modifying the `PROJECT_INFO` field in `src/config.js`:

```javascript
PROJECT_INFO: `This is a 3D avatar project with lip-sync capabilities built using React Three Fiber (R3F).
The project features a talking avatar that can respond to user messages and animate its mouth in sync with speech.
...
`
```

This information helps the AI understand the context of the project and provide more relevant answers to project-related questions.

## How It Works

1. When a user sends a message, the chatbot sends it to the Gemini API along with:
   - A system prompt defining the chatbot's role as a friendly assistant
   - Project information to provide context
   - The current date and time for accurate temporal responses
   - Instructions to keep responses concise and friendly

2. The Gemini API processes the request and returns a response, which the chatbot displays to the user.

3. The avatar animates its mouth in sync with the spoken response, creating a lifelike interaction.

## Response Handling

The chatbot is designed to:

1. Send all questions to the Gemini API
2. Include project context with every request
3. Parse and display the API response
4. Handle errors gracefully if the API is unavailable

## Troubleshooting

- If you see "I'm not able to answer that right now" messages, check that your API key is correctly set
- If responses are slow, you can adjust the simulated thinking delay in the `getAIResponse` function
- Check the browser console for any API error messages

## Customizing the Chatbot Behavior

You can modify the system prompt in the `getAIResponse` function to change how the chatbot responds:

```javascript
const systemPrompt = `
You are a helpful AI assistant for a 3D avatar project.
You should respond directly without greetings like "Hey there", "Hi", or "Hello".

Here's information about the project you're part of:
${CONFIG.PROJECT_INFO}

Current date: ${formattedDate}
Current time: ${formattedTime}

User question: ${input}

IMPORTANT: Provide a direct answer without any greeting phrases like "Hey there", "Hi", or "Hello".
Start your response with the actual answer. Keep responses concise (1-3 sentences when possible).
If asked about the current date or time, use the date and time provided above.
`;
```

Adjusting this prompt can change the tone, style, and content of the chatbot's responses.
