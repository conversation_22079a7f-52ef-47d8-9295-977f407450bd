import React, { useState, useRef } from 'react';
import './AvatarGenerator.css';

const AvatarGenerator = ({ onAvatarGenerated, onClose }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [text, setText] = useState('Hello! I am your AI avatar.');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [error, setError] = useState('');
  const [generatedVideo, setGeneratedVideo] = useState(null);
  const fileInputRef = useRef(null);

  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a valid image file (JPEG, PNG, or GIF)');
        return;
      }

      // Validate file size (16MB max)
      const maxSize = 16 * 1024 * 1024;
      if (file.size > maxSize) {
        setError('Image file is too large. Please select an image smaller than 16MB.');
        return;
      }

      setSelectedImage(file);
      setError('');

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      // Simulate file input change
      const fakeEvent = {
        target: {
          files: [file]
        }
      };
      handleImageSelect(fakeEvent);
    }
  };

  const uploadImage = async (file) => {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch('http://localhost:5000/upload-image', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to upload image');
    }

    return await response.json();
  };

  const generateAvatar = async (fileId, text) => {
    const response = await fetch('http://localhost:5000/generate-avatar', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_id: fileId,
        text: text,
        voice_settings: {
          rate: 150,
          volume: 0.8
        }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to generate avatar');
    }

    return await response.json();
  };

  const handleGenerate = async () => {
    if (!selectedImage) {
      setError('Please select an image first');
      return;
    }

    if (!text.trim()) {
      setError('Please enter some text for the avatar to speak');
      return;
    }

    setIsGenerating(true);
    setError('');
    setGenerationProgress('Uploading image...');

    try {
      // Upload image
      const uploadResult = await uploadImage(selectedImage);
      
      setGenerationProgress('Generating talking avatar...');
      
      // Generate avatar
      const generateResult = await generateAvatar(uploadResult.file_id, text);
      
      if (generateResult.success) {
        setGenerationProgress('Avatar generated successfully!');
        setGeneratedVideo(generateResult.video_path);
        
        // Notify parent component
        if (onAvatarGenerated) {
          onAvatarGenerated({
            videoPath: generateResult.video_path,
            outputId: generateResult.output_id,
            processingTime: generateResult.processing_time
          });
        }
      } else {
        throw new Error(generateResult.error || 'Generation failed');
      }

    } catch (err) {
      setError(err.message);
      setGenerationProgress('');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleReset = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setText('Hello! I am your AI avatar.');
    setError('');
    setGenerationProgress('');
    setGeneratedVideo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="avatar-generator-overlay">
      <div className="avatar-generator-modal">
        <div className="avatar-generator-header">
          <h2>Generate Talking Avatar</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="avatar-generator-content">
          {/* Image Upload Section */}
          <div className="upload-section">
            <h3>1. Upload Your Image</h3>
            <div 
              className={`upload-area ${imagePreview ? 'has-image' : ''}`}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              {imagePreview ? (
                <div className="image-preview">
                  <img src={imagePreview} alt="Selected" />
                  <div className="image-overlay">
                    <span>Click to change image</span>
                  </div>
                </div>
              ) : (
                <div className="upload-placeholder">
                  <div className="upload-icon">📷</div>
                  <p>Click to select or drag & drop an image</p>
                  <p className="upload-hint">Supports JPEG, PNG, GIF (max 16MB)</p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageSelect}
              style={{ display: 'none' }}
            />
          </div>

          {/* Text Input Section */}
          <div className="text-section">
            <h3>2. Enter Text to Speak</h3>
            <textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter the text you want your avatar to speak..."
              rows={4}
              maxLength={500}
              disabled={isGenerating}
            />
            <div className="character-count">{text.length}/500</div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}

          {/* Progress Display */}
          {generationProgress && (
            <div className="progress-message">
              <span className="progress-icon">⏳</span>
              {generationProgress}
            </div>
          )}

          {/* Generated Video Display */}
          {generatedVideo && (
            <div className="result-section">
              <h3>Generated Avatar</h3>
              <div className="video-container">
                <video controls width="100%">
                  <source src={`http://localhost:5000${generatedVideo}`} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              <div className="download-section">
                <a 
                  href={`http://localhost:5000${generatedVideo}`} 
                  download 
                  className="download-button"
                >
                  Download Video
                </a>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="action-buttons">
            <button 
              className="reset-button" 
              onClick={handleReset}
              disabled={isGenerating}
            >
              Reset
            </button>
            <button 
              className="generate-button" 
              onClick={handleGenerate}
              disabled={isGenerating || !selectedImage || !text.trim()}
            >
              {isGenerating ? 'Generating...' : 'Generate Avatar'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarGenerator;
