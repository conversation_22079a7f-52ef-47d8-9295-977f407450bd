#!/bin/bash

echo "Starting Talking Face Avatar Application..."
echo

echo "[1/3] Starting Backend Server..."
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements if needed
if [ ! -f ".requirements_installed" ]; then
    echo "Installing Python requirements..."
    pip install -r requirements.txt
    touch .requirements_installed
fi

# Start backend in background
python app.py &
BACKEND_PID=$!
cd ..

echo "[2/3] Waiting for backend to start..."
sleep 3

echo "[3/3] Starting Frontend Development Server..."
npm run dev &
FRONTEND_PID=$!

echo
echo "Both servers are running..."
echo "Frontend: http://localhost:5173"
echo "Backend: http://localhost:5000"
echo
echo "Press Ctrl+C to stop both servers..."

# Function to cleanup processes
cleanup() {
    echo
    echo "Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "Servers stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
