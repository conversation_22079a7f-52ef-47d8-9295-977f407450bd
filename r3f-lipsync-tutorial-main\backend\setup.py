#!/usr/bin/env python3
"""
Setup script for Talking Face Avatar Backend
This script sets up the Python environment and downloads required models
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import tarfile
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    logger.info(f"Python version: {sys.version}")
    return True

def install_requirements():
    """Install Python requirements"""
    try:
        logger.info("Installing Python requirements...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "uploads",
        "outputs", 
        "checkpoints",
        "logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def download_file(url, destination):
    """Download a file from URL to destination"""
    try:
        logger.info(f"Downloading {url}...")
        urllib.request.urlretrieve(url, destination)
        logger.info(f"Downloaded to {destination}")
        return True
    except Exception as e:
        logger.error(f"Failed to download {url}: {e}")
        return False

def download_models():
    """Download SadTalker model files"""
    logger.info("Setting up SadTalker models...")
    
    # Create checkpoints directory
    checkpoint_dir = "checkpoints"
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # Model files to download (these are example URLs - in a real implementation, 
    # you would use the actual SadTalker model URLs)
    models = {
        "shape_predictor_68_face_landmarks.dat": {
            "url": "http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2",
            "compressed": True,
            "type": "bz2"
        }
    }
    
    # For now, we'll create placeholder files since we don't have access to the actual models
    placeholder_models = [
        "auido2exp_00300-model.pth",
        "auido2pose_00140-model.pth", 
        "mapping_00229-model.pth.tar",
        "facevid2vid_00189-model.pth.tar",
        "epoch_20.pth"
    ]
    
    for model_name in placeholder_models:
        model_path = os.path.join(checkpoint_dir, model_name)
        if not os.path.exists(model_path):
            # Create placeholder file
            with open(model_path, 'w') as f:
                f.write(f"# Placeholder for {model_name}\n")
                f.write("# In a real implementation, this would be the actual model file\n")
            logger.info(f"Created placeholder: {model_path}")
    
    logger.info("Model setup completed (using placeholders)")
    return True

def test_installation():
    """Test if the installation is working"""
    try:
        logger.info("Testing installation...")
        
        # Test imports
        import flask
        import PIL
        import cv2
        import numpy as np
        
        logger.info("All required packages imported successfully")
        
        # Test SadTalker inference
        from sadtalker_inference import SadTalkerInference
        sadtalker = SadTalkerInference()
        logger.info("SadTalker inference module loaded successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

def main():
    """Main setup function"""
    logger.info("Starting Talking Face Avatar Backend Setup...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        logger.error("Failed to install requirements")
        sys.exit(1)
    
    # Download models
    if not download_models():
        logger.error("Failed to download models")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        logger.error("Installation test failed")
        sys.exit(1)
    
    logger.info("Setup completed successfully!")
    logger.info("You can now run the backend with: python app.py")

if __name__ == "__main__":
    main()
