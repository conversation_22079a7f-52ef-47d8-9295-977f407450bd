# Talking Face Avatar Backend

This backend service provides image-to-avatar conversion functionality using SadTalker technology. It allows users to upload an image and generate a talking face video from text input.

## Features

- **Image Upload**: Upload portrait images for avatar generation
- **Text-to-Speech**: Convert text to speech with customizable voice settings
- **Avatar Generation**: Create talking face videos using SadTalker technology
- **Video Download**: Download generated avatar videos
- **RESTful API**: Clean API endpoints for frontend integration

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- FFmpeg (for video processing)
- CUDA-compatible GPU (recommended for faster processing)

## Installation

### 1. Clone and Navigate

```bash
cd r3f-lipsync-tutorial-main/backend
```

### 2. Create Virtual Environment (Recommended)

```bash
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

### 3. Run Setup Script

```bash
python setup.py
```

This will:
- Install all required Python packages
- Create necessary directories
- Download model files (placeholder implementation)
- Test the installation

### 4. Manual Installation (Alternative)

If the setup script fails, you can install manually:

```bash
pip install -r requirements.txt
```

## Configuration

### Model Files

The SadTalker models should be placed in the `checkpoints/` directory:

- `auido2exp_00300-model.pth` - Audio to expression model
- `auido2pose_00140-model.pth` - Audio to pose model  
- `mapping_00229-model.pth.tar` - Mapping network
- `facevid2vid_00189-model.pth.tar` - Face video-to-video model
- `epoch_20.pth` - 3D face reconstruction model
- `shape_predictor_68_face_landmarks.dat` - Face landmark detector

### Environment Variables

You can configure the service using environment variables:

```bash
export FLASK_ENV=development
export FLASK_DEBUG=1
export UPLOAD_FOLDER=uploads
export OUTPUT_FOLDER=outputs
```

## Usage

### Start the Server

```bash
python app.py
```

The server will start on `http://localhost:5000`

### API Endpoints

#### 1. Health Check
```
GET /health
```

#### 2. Upload Image
```
POST /upload-image
Content-Type: multipart/form-data

Body:
- image: Image file (JPEG, PNG, GIF, max 16MB)
```

#### 3. Generate Avatar
```
POST /generate-avatar
Content-Type: application/json

Body:
{
  "file_id": "uploaded_file_id",
  "text": "Hello, I am your AI avatar!",
  "voice_settings": {
    "rate": 150,
    "volume": 0.8
  }
}
```

#### 4. Download Video
```
GET /download/<filename>
```

#### 5. List Generated Avatars
```
GET /list-avatars
```

## Development

### Project Structure

```
backend/
├── app.py                 # Main Flask application
├── sadtalker_inference.py # SadTalker integration
├── requirements.txt       # Python dependencies
├── setup.py              # Setup script
├── uploads/              # Uploaded images
├── outputs/              # Generated videos
├── checkpoints/          # Model files
└── logs/                 # Log files
```

### Adding New Features

1. **Custom Voice Models**: Integrate with services like ElevenLabs
2. **Face Enhancement**: Add GFPGAN for face restoration
3. **Batch Processing**: Support multiple image processing
4. **Real-time Generation**: WebSocket support for live updates

### Testing

```bash
# Test the API endpoints
curl -X GET http://localhost:5000/health

# Upload an image
curl -X POST -F "image=@test_image.jpg" http://localhost:5000/upload-image

# Generate avatar
curl -X POST -H "Content-Type: application/json" \
  -d '{"file_id":"your_file_id","text":"Hello world!"}' \
  http://localhost:5000/generate-avatar
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **CUDA Issues**: If you have GPU issues, install CPU-only PyTorch
   ```bash
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
   ```

3. **FFmpeg Not Found**: Install FFmpeg
   ```bash
   # Windows (using chocolatey)
   choco install ffmpeg
   
   # macOS (using homebrew)
   brew install ffmpeg
   
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg
   ```

4. **Model Files Missing**: The current implementation uses placeholder models. For production, you need to download the actual SadTalker models.

### Logs

Check the application logs for detailed error information:
- Console output shows real-time logs
- Check the `logs/` directory for persistent logs

## Production Deployment

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "app.py"]
```

### Environment Configuration

For production, use environment variables:
- Set `FLASK_ENV=production`
- Configure proper logging
- Use a production WSGI server like Gunicorn
- Set up reverse proxy with Nginx

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs
3. Create an issue with detailed information
