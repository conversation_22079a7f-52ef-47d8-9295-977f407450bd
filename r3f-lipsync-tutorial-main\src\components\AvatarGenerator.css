.avatar-generator-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.avatar-generator-modal {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.avatar-generator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.avatar-generator-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #e0e0e0;
  color: #333;
}

.avatar-generator-content {
  padding: 24px;
}

.upload-section,
.text-section,
.result-section {
  margin-bottom: 24px;
}

.upload-section h3,
.text-section h3,
.result-section h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.upload-area {
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  position: relative;
  overflow: hidden;
}

.upload-area:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.upload-area.has-image {
  padding: 0;
  border: 2px solid #007bff;
}

.upload-placeholder {
  color: #666;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.upload-placeholder p {
  margin: 8px 0;
}

.upload-hint {
  font-size: 0.9rem;
  color: #999;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200px;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 6px;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-overlay span {
  color: white;
  font-weight: 500;
}

.text-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.text-section textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.text-section textarea:disabled {
  background: #f5f5f5;
  color: #666;
}

.character-count {
  text-align: right;
  font-size: 0.85rem;
  color: #666;
  margin-top: 4px;
}

.error-message,
.progress-message {
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  color: #c53030;
}

.progress-message {
  background: #f0f8ff;
  border: 1px solid #bee3f8;
  color: #2b6cb0;
}

.error-icon,
.progress-icon {
  font-size: 16px;
}

.video-container {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.video-container video {
  display: block;
  width: 100%;
  height: auto;
}

.download-section {
  text-align: center;
  margin-top: 16px;
}

.download-button {
  display: inline-block;
  padding: 10px 20px;
  background: #28a745;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.download-button:hover {
  background: #218838;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.reset-button,
.generate-button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.reset-button {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #d0d0d0;
}

.reset-button:hover:not(:disabled) {
  background: #e9ecef;
  color: #333;
}

.generate-button {
  background: #007bff;
  color: white;
}

.generate-button:hover:not(:disabled) {
  background: #0056b3;
}

.reset-button:disabled,
.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .avatar-generator-overlay {
    padding: 10px;
  }
  
  .avatar-generator-modal {
    max-height: 95vh;
  }
  
  .avatar-generator-content {
    padding: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .reset-button,
  .generate-button {
    width: 100%;
  }
}
