.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.ui-overlay > * {
  pointer-events: auto;
}

/* Avatar Name Styling */
.avatar-name-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1001;
}

.avatar-name {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.avatar-name:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.avatar-name span {
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.edit-name-btn {
  background: none;
  border: none;
  color: #3a7bd5;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.avatar-name:hover .edit-name-btn {
  opacity: 1;
}

.name-input-container {
  display: flex;
  align-items: center;
}

.name-input-container input {
  border: none;
  outline: none;
  background: transparent;
  padding: 5px;
  font-size: 14px;
  width: 150px;
}

.name-input-container button {
  background: none;
  border: none;
  color: #3a7bd5;
  cursor: pointer;
  font-size: 14px;
  margin-left: 5px;
  transition: color 0.2s;
}

.name-input-container button:hover {
  color: #2d5a9c;
}

.chatbot-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 500px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.message {
  margin-bottom: 10px;
  max-width: 80%;
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.user-message {
  align-self: flex-end;
  background-color: #3a7bd5;
  color: white;
  border-bottom-right-radius: 5px;
}

.bot-message {
  align-self: flex-start;
  background-color: #e1f0ff;
  color: #333;
  border-bottom-left-radius: 5px;
}

.message-content {
  word-wrap: break-word;
}

.message-timestamp {
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: -18px;
  right: 5px;
}

.user-message .message-timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.chat-input-container {
  display: flex;
  padding: 10px;
  border-top: 1px solid #e0e0e0;
  background-color: white;
}

#user-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

#user-input:focus {
  border-color: #3a7bd5;
}

#send-button {
  margin-left: 10px;
  padding: 10px 15px;
  background-color: #3a7bd5;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

#send-button:hover {
  background-color: #2d5a9c;
}

#send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.thinking-indicator {
  display: flex;
  align-self: flex-start;
  background-color: #e1f0ff;
  padding: 15px;
  border-radius: 18px;
  margin-bottom: 10px;
}

.thinking-dot {
  width: 8px;
  height: 8px;
  margin: 0 3px;
  background-color: #3a7bd5;
  border-radius: 50%;
  animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes thinking {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}
