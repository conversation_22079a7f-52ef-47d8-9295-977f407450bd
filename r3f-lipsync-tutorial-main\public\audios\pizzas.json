{"metadata": {"soundFile": "/Users/<USER>/Documents/Projects/wawasensei/r3f-lipsync-tutorial/Rhubarb-Lip-Sync-1.13.0-macOS/../public/audios/pizzas.ogg", "duration": 6.92}, "mouthCues": [{"start": 0.0, "end": 0.04, "value": "X"}, {"start": 0.04, "end": 0.26, "value": "F"}, {"start": 0.26, "end": 0.33, "value": "C"}, {"start": 0.33, "end": 0.41, "value": "A"}, {"start": 0.41, "end": 0.66, "value": "B"}, {"start": 0.66, "end": 0.73, "value": "C"}, {"start": 0.73, "end": 0.87, "value": "B"}, {"start": 0.87, "end": 1.19, "value": "X"}, {"start": 1.19, "end": 1.26, "value": "B"}, {"start": 1.26, "end": 1.33, "value": "A"}, {"start": 1.33, "end": 1.5, "value": "B"}, {"start": 1.5, "end": 1.95, "value": "X"}, {"start": 1.95, "end": 2.28, "value": "B"}, {"start": 2.28, "end": 2.36, "value": "A"}, {"start": 2.36, "end": 2.63, "value": "B"}, {"start": 2.63, "end": 2.7, "value": "C"}, {"start": 2.7, "end": 2.84, "value": "B"}, {"start": 2.84, "end": 2.9, "value": "A"}, {"start": 2.9, "end": 2.96, "value": "C"}, {"start": 2.96, "end": 3.04, "value": "A"}, {"start": 3.04, "end": 3.21, "value": "E"}, {"start": 3.21, "end": 3.28, "value": "F"}, {"start": 3.28, "end": 3.75, "value": "B"}, {"start": 3.75, "end": 3.82, "value": "D"}, {"start": 3.82, "end": 4.02, "value": "B"}, {"start": 4.02, "end": 4.16, "value": "C"}, {"start": 4.16, "end": 4.23, "value": "B"}, {"start": 4.23, "end": 4.31, "value": "A"}, {"start": 4.31, "end": 4.51, "value": "C"}, {"start": 4.51, "end": 4.58, "value": "B"}, {"start": 4.58, "end": 4.65, "value": "C"}, {"start": 4.65, "end": 4.75, "value": "A"}, {"start": 4.75, "end": 4.88, "value": "B"}, {"start": 4.88, "end": 5.28, "value": "X"}, {"start": 5.28, "end": 5.41, "value": "F"}, {"start": 5.41, "end": 5.62, "value": "C"}, {"start": 5.62, "end": 5.9, "value": "B"}, {"start": 5.9, "end": 5.97, "value": "E"}, {"start": 5.97, "end": 6.04, "value": "F"}, {"start": 6.04, "end": 6.11, "value": "C"}, {"start": 6.11, "end": 6.18, "value": "B"}, {"start": 6.18, "end": 6.26, "value": "A"}, {"start": 6.26, "end": 6.51, "value": "B"}, {"start": 6.51, "end": 6.65, "value": "C"}, {"start": 6.65, "end": 6.92, "value": "X"}]}