"""
Flask backend for Talking Face Avatar generation
Integrates SadTalker technology for image-to-avatar conversion
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import tempfile
import uuid
from werkzeug.utils import secure_filename
import subprocess
import json
import logging
from pathlib import Path
from sadtalker_inference import SadTalkerInference

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# Initialize SadTalker inference
sadtalker = SadTalkerInference()

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_image(file_path):
    """Validate uploaded image file"""
    try:
        from PIL import Image
        with Image.open(file_path) as img:
            # Check image dimensions (reasonable limits)
            if img.width > 2048 or img.height > 2048:
                return False, "Image too large (max 2048x2048)"
            if img.width < 256 or img.height < 256:
                return False, "Image too small (min 256x256)"
            return True, "Valid image"
    except Exception as e:
        return False, f"Invalid image file: {str(e)}"

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "talking-face-avatar"})

@app.route('/upload-image', methods=['POST'])
def upload_image():
    """Upload and validate image for avatar generation"""
    try:
        if 'image' not in request.files:
            return jsonify({"error": "No image file provided"}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        if not allowed_file(file.filename):
            return jsonify({"error": "Invalid file type. Allowed: png, jpg, jpeg, gif"}), 400

        # Generate unique filename
        file_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{file_id}.{file_extension}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)

        # Save file
        file.save(file_path)

        # Validate image
        is_valid, message = validate_image(file_path)
        if not is_valid:
            os.remove(file_path)
            return jsonify({"error": message}), 400

        logger.info(f"Image uploaded successfully: {unique_filename}")
        return jsonify({
            "success": True,
            "file_id": file_id,
            "filename": unique_filename,
            "message": "Image uploaded successfully"
        })

    except Exception as e:
        logger.error(f"Error uploading image: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/generate-avatar', methods=['POST'])
def generate_avatar():
    """Generate talking avatar from uploaded image and audio"""
    try:
        data = request.get_json()

        if not data or 'file_id' not in data:
            return jsonify({"error": "file_id is required"}), 400

        file_id = data['file_id']
        text = data.get('text', 'Hello, I am your AI avatar!')
        voice_settings = data.get('voice_settings', {})

        # Find the uploaded image
        image_files = [f for f in os.listdir(UPLOAD_FOLDER) if f.startswith(file_id)]
        if not image_files:
            return jsonify({"error": "Image file not found"}), 404

        image_path = os.path.join(UPLOAD_FOLDER, image_files[0])

        # Generate unique output filename
        output_id = str(uuid.uuid4())
        output_video_path = os.path.join(OUTPUT_FOLDER, f"{output_id}.mp4")

        # Generate talking face avatar using SadTalker
        logger.info(f"Generating avatar for image: {image_path}, text: {text}")

        import time
        start_time = time.time()

        # Use SadTalker to generate the talking face video
        success, message = sadtalker.generate_from_text(
            image_path=image_path,
            text=text,
            output_path=output_video_path,
            voice_settings=voice_settings
        )

        processing_time = time.time() - start_time

        if success:
            result = {
                "success": True,
                "output_id": output_id,
                "video_path": f"/download/{output_id}.mp4",
                "message": message,
                "processing_time": f"{processing_time:.1f}s"
            }
        else:
            result = {
                "success": False,
                "error": message,
                "processing_time": f"{processing_time:.1f}s"
            }

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error generating avatar: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    """Download generated avatar video"""
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/list-avatars', methods=['GET'])
def list_avatars():
    """List all generated avatars"""
    try:
        avatars = []
        for filename in os.listdir(OUTPUT_FOLDER):
            if filename.endswith('.mp4'):
                file_path = os.path.join(OUTPUT_FOLDER, filename)
                file_stats = os.stat(file_path)
                avatars.append({
                    "filename": filename,
                    "size": file_stats.st_size,
                    "created": file_stats.st_ctime,
                    "download_url": f"/download/{filename}"
                })

        return jsonify({"avatars": avatars})

    except Exception as e:
        logger.error(f"Error listing avatars: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
