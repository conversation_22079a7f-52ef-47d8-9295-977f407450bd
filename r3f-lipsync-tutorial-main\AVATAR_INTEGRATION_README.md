# Talking Face Avatar Integration

This project integrates the Talking Face Avatar functionality from the SadTalker project into the existing React Three Fiber (R3F) avatar chatbot. Users can now upload an image and generate a talking avatar video while maintaining the existing 3D avatar interface.

## 🚀 New Features Added

### Image-to-Avatar Conversion
- Upload portrait images (JPEG, PNG, GIF)
- Generate talking face videos from text input
- Download generated avatar videos
- Maintain existing 3D avatar functionality

### Backend Service
- Flask-based Python backend
- SadTalker integration for video generation
- RESTful API for frontend communication
- Image validation and processing

### Frontend Integration
- New "Generate Avatar" button in the UI
- Modal interface for image upload and text input
- Progress tracking for avatar generation
- Video preview and download functionality

## 📁 Project Structure

```
r3f-lipsync-tutorial-main/
├── backend/                          # Python backend service
│   ├── app.py                       # Main Flask application
│   ├── sadtalker_inference.py       # SadTalker integration
│   ├── requirements.txt             # Python dependencies
│   ├── setup.py                     # Backend setup script
│   ├── README.md                    # Backend documentation
│   ├── uploads/                     # Uploaded images
│   ├── outputs/                     # Generated videos
│   └── checkpoints/                 # Model files
├── src/
│   ├── components/
│   │   ├── Avatar.jsx               # Existing 3D avatar (unchanged)
│   │   ├── AvatarGenerator.jsx      # New image-to-avatar component
│   │   ├── AvatarGenerator.css      # Styles for avatar generator
│   │   ├── Chatbot.jsx             # Existing chatbot (unchanged)
│   │   └── Experience.jsx          # Existing 3D scene (unchanged)
│   ├── App.jsx                      # Updated with avatar generator
│   ├── chatbot.css                  # Updated with new button styles
│   └── config.js                    # Existing config (unchanged)
└── package.json                     # Frontend dependencies
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 16+ and npm/yarn
- Python 3.8+
- FFmpeg (for video processing)

### 1. Frontend Setup (Existing)
```bash
cd r3f-lipsync-tutorial-main
npm install
npm run dev
```

### 2. Backend Setup (New)
```bash
cd r3f-lipsync-tutorial-main/backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Run setup script
python setup.py

# Or install manually:
pip install -r requirements.txt

# Start the backend server
python app.py
```

The backend will run on `http://localhost:5000`

## 🎯 How to Use

### 1. Access the Application
- Start both frontend (`npm run dev`) and backend (`python app.py`)
- Open `http://localhost:5173` in your browser

### 2. Generate Avatar from Image
1. Click the "🎭 Generate Avatar" button (top-right corner)
2. Upload a portrait image (drag & drop or click to select)
3. Enter text for the avatar to speak
4. Click "Generate Avatar"
5. Wait for processing (progress will be shown)
6. Preview and download the generated video

### 3. Use Existing 3D Avatar
- The existing 3D avatar functionality remains unchanged
- Chat with the avatar using the chatbot interface
- Avatar will respond with lip-sync and animations

## 🔧 Technical Implementation

### Backend Architecture
- **Flask**: Web framework for API endpoints
- **SadTalker**: Core technology for talking face generation
- **OpenCV**: Image processing and video creation
- **PIL**: Image validation and preprocessing
- **PyTorch**: Deep learning framework for models

### Frontend Integration
- **React**: Component-based UI updates
- **Three.js/R3F**: Existing 3D avatar rendering
- **CSS**: Responsive modal and button styling
- **Fetch API**: Communication with backend

### API Endpoints
- `POST /upload-image` - Upload and validate images
- `POST /generate-avatar` - Generate talking face video
- `GET /download/<filename>` - Download generated videos
- `GET /list-avatars` - List all generated avatars
- `GET /health` - Health check

## 🎨 UI/UX Features

### Avatar Generator Modal
- Drag & drop image upload
- Real-time image preview
- Text input with character counter
- Progress tracking during generation
- Video preview with download option
- Responsive design for mobile devices

### Integration with Existing UI
- Non-intrusive button placement
- Maintains existing avatar functionality
- Consistent styling with current theme
- Smooth modal animations

## 🔄 Workflow

1. **Image Upload**: User selects portrait image
2. **Validation**: Backend validates image format and size
3. **Text Input**: User enters text for speech
4. **Processing**: SadTalker generates talking face video
5. **Download**: User can preview and download result
6. **Integration**: Generated videos can be used alongside 3D avatar

## 🚧 Current Implementation Status

### ✅ Completed
- Backend Flask application structure
- Frontend React components and UI
- Image upload and validation
- API endpoint definitions
- Modal interface and styling
- Integration with existing codebase

### 🔄 In Progress (Placeholder Implementation)
- Actual SadTalker model integration
- Real video generation (currently creates placeholder videos)
- Model file downloading and setup

### 📋 Next Steps for Full Implementation
1. **Download SadTalker Models**: Get actual model files
2. **Complete Integration**: Implement real SadTalker inference
3. **Voice Enhancement**: Integrate with ElevenLabs or similar TTS
4. **Face Enhancement**: Add GFPGAN for better face quality
5. **Performance Optimization**: GPU acceleration and caching

## 🔧 Configuration

### Backend Configuration
Edit `backend/app.py` for:
- Upload folder paths
- File size limits
- Allowed file types
- Model checkpoint paths

### Frontend Configuration
Edit `src/config.js` for:
- API endpoints
- Default settings
- Voice configuration

## 🐛 Troubleshooting

### Common Issues
1. **Backend not starting**: Check Python dependencies
2. **CORS errors**: Ensure Flask-CORS is installed
3. **Upload failures**: Check file size and format
4. **Model errors**: Verify checkpoint files exist

### Development Tips
- Use browser dev tools to monitor API calls
- Check backend console for detailed error logs
- Ensure both frontend and backend are running
- Test with small images first

## 🤝 Contributing

To extend this integration:

1. **Add new voice models**: Integrate ElevenLabs API
2. **Improve face detection**: Add better preprocessing
3. **Batch processing**: Support multiple images
4. **Real-time preview**: WebSocket for live updates
5. **Cloud deployment**: Docker and cloud setup

## 📄 License

This integration maintains the same license as the original project.

## 🙏 Acknowledgments

- Original R3F Avatar project
- SadTalker project by OpenTalker
- React Three Fiber community
- Flask and Python ecosystem
