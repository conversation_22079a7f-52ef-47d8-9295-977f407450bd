import { Canvas } from "@react-three/fiber";
import { Experience } from "./components/Experience";
import Chatbot from "./components/Chatbot";
import AvatarGenerator from "./components/AvatarGenerator";
import React, { useRef, useState, useEffect } from "react";
import CONFIG from "./config";
import "./chatbot.css";

function App() {
  const avatarRef = useRef();
  const [avatarName, setAvatarName] = useState(CONFIG.DEFAULT_AVATAR_NAME);
  const [showNameInput, setShowNameInput] = useState(false);
  const [nameInputValue, setNameInputValue] = useState("");
  const [showAvatarGenerator, setShowAvatarGenerator] = useState(false);
  const [generatedAvatars, setGeneratedAvatars] = useState([]);

  // Load saved name from localStorage if available
  useEffect(() => {
    const savedName = localStorage.getItem("avatarName");
    if (savedName) {
      setAvatarName(savedName);
    }
  }, []);

  // Save name to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("avatarName", avatarName);
  }, [avatarName]);

  // Handle name change
  const handleNameChange = (newName) => {
    if (newName && newName.trim() !== "") {
      setAvatarName(newName.trim());
      setShowNameInput(false);
    }
  };

  // Toggle name input visibility
  const toggleNameInput = () => {
    setNameInputValue(avatarName);
    setShowNameInput(!showNameInput);
  };

  // Handle messages from the chatbot
  const handleMessageReceived = (message) => {
    if (avatarRef.current) {
      avatarRef.current.handleNewMessage(message);
    }
  };

  // Handle thinking state
  const handleThinking = () => {
    if (avatarRef.current) {
      avatarRef.current.handleThinking();
    }
  };

  // Handle stop thinking state
  const handleStopThinking = () => {
    if (avatarRef.current) {
      avatarRef.current.handleStopThinking();
    }
  };

  // Handle avatar generation
  const handleAvatarGenerated = (avatarData) => {
    setGeneratedAvatars(prev => [...prev, avatarData]);
    console.log('Avatar generated:', avatarData);
  };

  // Toggle avatar generator
  const toggleAvatarGenerator = () => {
    setShowAvatarGenerator(!showAvatarGenerator);
  };

  return (
    <div className="app-container">
      {/* 3D Canvas */}
      <Canvas shadows camera={{ position: [0, 0, 8], fov: 42 }}>
        <color attach="background" args={["#ececec"]} />
        <Experience avatarRef={avatarRef} />
      </Canvas>

      {/* Avatar Name Display */}
      <div className="avatar-name-container">
        <div className="avatar-name" onClick={toggleNameInput}>
          {showNameInput ? (
            <div className="name-input-container">
              <input
                type="text"
                value={nameInputValue}
                onChange={(e) => setNameInputValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleNameChange(nameInputValue);
                  }
                }}
                autoFocus
                placeholder="Enter avatar name"
                maxLength={20}
              />
              <button onClick={() => handleNameChange(nameInputValue)}>✓</button>
              <button onClick={() => setShowNameInput(false)}>✕</button>
            </div>
          ) : (
            <>
              <span>{avatarName}</span>
              <button className="edit-name-btn" title="Edit name">✎</button>
            </>
          )}
        </div>
      </div>

      {/* Avatar Generator Button */}
      <div className="avatar-generator-button-container">
        <button
          className="avatar-generator-button"
          onClick={toggleAvatarGenerator}
          title="Generate Avatar from Image"
        >
          🎭 Generate Avatar
        </button>
      </div>

      {/* HTML UI overlay */}
      <div className="ui-overlay">
        <Chatbot
          avatarName={avatarName}
          onMessageReceived={handleMessageReceived}
          onThinking={handleThinking}
          onStopThinking={handleStopThinking}
          onNameChange={handleNameChange}
        />
      </div>

      {/* Avatar Generator Modal */}
      {showAvatarGenerator && (
        <AvatarGenerator
          onAvatarGenerated={handleAvatarGenerated}
          onClose={() => setShowAvatarGenerator(false)}
        />
      )}
    </div>
  );
}

export default App;
