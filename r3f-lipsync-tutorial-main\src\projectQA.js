/**
 * Project-specific questions and answers
 * This file contains predefined Q&A pairs for questions related to this project.
 */

const PROJECT_QA = [
  {
    keywords: ['what is this project', 'about this project', 'project about', 'purpose of project'],
    response: "This project is a 3D avatar with lip-sync capabilities built using React Three Fiber (R3F). It features a talking avatar that can respond to user messages and animate its mouth in sync with speech."
  },
  {
    keywords: ['how does lip sync work', 'lip sync', 'lipsync', 'mouth animation', 'how does the mouth move'],
    response: "The lip-sync in this project works by mapping text or audio to visemes (visual representations of phonemes). The avatar's mouth shape changes based on the sounds being spoken, using morph targets in the 3D model."
  },
  {
    keywords: ['technologies used', 'tech stack', 'libraries', 'frameworks', 'what technologies'],
    response: "This project uses React for the UI, Three.js for 3D rendering, React Three Fiber as a React wrapper for Three.js, and various animation libraries. It also uses the Web Speech API for text-to-speech functionality."
  },
  {
    keywords: ['react three fiber', 'r3f', 'what is r3f', 'threejs', 'three.js'],
    response: "React Three Fiber (R3F) is a React renderer for Three.js, making it easier to build Three.js scenes declaratively using React components. It's the core technology that powers the 3D avatar in this project."
  },
  {
    keywords: ['avatar model', '3d model', 'where is the model from', 'model source'],
    response: "The 3D avatar model used in this project is a custom model. The model includes morph targets (blend shapes) for different mouth positions that enable the lip-sync functionality."
  },
  {
    keywords: ['animations', 'animation system', 'how animations work'],
    response: "The animations in this project are handled through a combination of Three.js animation system and custom logic. The avatar can perform idle animations, greeting gestures, and mouth movements synchronized with speech."
  },
  {
    keywords: ['visemes', 'phonemes', 'mouth shapes', 'speech animation'],
    response: "Visemes are visual representations of phonemes (speech sounds). This project maps speech to a set of visemes like 'viseme_PP', 'viseme_kk', 'viseme_I', etc., which correspond to different mouth shapes in the 3D model."
  },
  {
    keywords: ['how to customize', 'change avatar', 'modify avatar', 'customize'],
    response: "You can customize the avatar by modifying the CONFIG object in src/config.js. You can change colors, animation speed, and other parameters. To use a different 3D model, you would need to replace the model file and update the Avatar component."
  },
  {
    keywords: ['chatbot', 'how does the chatbot work', 'chat functionality'],
    response: "The chatbot in this project uses a combination of predefined responses for project-related questions and the Gemini API for general questions. It analyzes user input, matches keywords, and generates appropriate responses."
  },
  {
    keywords: ['speech synthesis', 'text to speech', 'voice', 'speaking'],
    response: "The avatar uses the Web Speech API's SpeechSynthesis interface to convert text responses into speech. When the chatbot generates a response, it's passed to the speech synthesis system, which then speaks the text while the avatar's mouth animates in sync."
  },
  {
    keywords: ['performance', 'optimization', 'lag', 'slow'],
    response: "The project is optimized for performance by using efficient 3D rendering techniques. If you experience lag, try reducing the complexity of the scene or optimizing the 3D model. You can also adjust animation parameters in the config file."
  },
  {
    keywords: ['source code', 'github', 'repository', 'code'],
    response: "The source code for this project is available on GitHub. You can find it by searching for 'r3f-lipsync-tutorial' or checking the project documentation for the repository link."
  },
  {
    keywords: ['tutorial', 'learn how', 'how to build', 'create similar'],
    response: "This project is based on a tutorial that teaches how to create a talking 3D avatar with lip-sync in React Three Fiber. You can find the tutorial on YouTube by searching for 'r3f lipsync tutorial'."
  },
  {
    keywords: ['features', 'capabilities', 'what can it do'],
    response: "This project features a 3D avatar that can talk with lip-sync, respond to user messages, display different emotions through animations, and integrate with AI APIs for more dynamic conversations."
  },
  {
    keywords: ['how to run', 'start project', 'run locally', 'development'],
    response: "To run this project locally, clone the repository, install dependencies with 'npm install' or 'yarn', then start the development server with 'npm run dev' or 'yarn dev'. The project should open in your browser at localhost:5173."
  }
];

export default PROJECT_QA;
